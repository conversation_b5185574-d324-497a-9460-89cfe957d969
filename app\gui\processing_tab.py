"""
Processing tab module for the Satellite Telemetry Processor.
"""

import tkinter as tk
from tkinter import ttk, filedialog
import os
from datetime import datetime
import glob

from app.utils_pkg.common import show_error_message, show_info_message

class ProcessingTab:
    """Processing tab component for the application."""

    def __init__(self, notebook, app):
        """Initialize the processing tab.

        Args:
            notebook: The parent notebook widget
            app: The main application instance
        """
        self.notebook = notebook
        self.app = app

        # Create main frame
        self.frame = ttk.Frame(notebook)

        # Create processing sections
        self._create_drive_section()
        self._create_input_section()
        self._create_control_section()
        self._create_progress_section()

    def _create_drive_section(self):
        """Create the Google Drive import section."""
        drive_frame = ttk.LabelFrame(self.frame, text="Google Drive Import")
        drive_frame.pack(fill=tk.X, padx=10, pady=5)

        # Filter section
        filter_frame = ttk.LabelFrame(drive_frame, text="Filters")
        filter_frame.pack(fill=tk.X, padx=5, pady=5)

        # Create two rows for filters
        filter_row1 = ttk.Frame(filter_frame)
        filter_row1.pack(fill=tk.X, padx=5, pady=2)

        filter_row2 = ttk.Frame(filter_frame)
        filter_row2.pack(fill=tk.X, padx=5, pady=2)

        # Satellite name filter
        satellite_label = ttk.Label(filter_row1, text="Satellite Name:")
        satellite_label.pack(side=tk.LEFT, padx=5)

        self.satellite_filter_var = tk.StringVar(value="All")
        satellite_options = ["All", "Ribat", "Eosat"]
        satellite_combo = ttk.Combobox(
            filter_row1,
            textvariable=self.satellite_filter_var,
            values=satellite_options,
            state="readonly",
            width=15
        )
        satellite_combo.pack(side=tk.LEFT, padx=5)

        # Subsystem filter
        subsystem_label = ttk.Label(filter_row2, text="Subsystem:")
        subsystem_label.pack(side=tk.LEFT, padx=5)

        self.subsystem_filter_var = tk.StringVar(value="All")
        subsystem_options = ["All", "Eps", "Fc", "Pc", "Adcs", "Comm"]
        subsystem_combo = ttk.Combobox(
            filter_row2,
            textvariable=self.subsystem_filter_var,
            values=subsystem_options,
            state="readonly",
            width=15
        )
        subsystem_combo.pack(side=tk.LEFT, padx=5)

        # Clear filters button
        clear_filters_btn = ttk.Button(
            filter_row2,
            text="Clear Filters",
            command=self._clear_drive_filters
        )
        clear_filters_btn.pack(side=tk.RIGHT, padx=5)

        # Drive files list
        list_frame = ttk.Frame(drive_frame)
        list_frame.pack(fill=tk.BOTH, padx=5, pady=5)

        # Create listbox with scrollbar
        self.files_listbox = tk.Listbox(list_frame, height=5, selectmode=tk.SINGLE)
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.files_listbox.yview)
        self.files_listbox.configure(yscrollcommand=scrollbar.set)

        self.files_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Buttons frame
        button_frame = ttk.Frame(drive_frame)
        button_frame.pack(fill=tk.X, padx=5, pady=5)

        # List files button
        list_button = ttk.Button(
            button_frame,
            text="List Drive Files",
            command=self._list_drive_files
        )
        list_button.pack(side=tk.LEFT, padx=5)

        # Import button
        import_button = ttk.Button(
            button_frame,
            text="Import Selected",
            command=self._import_selected_file
        )
        import_button.pack(side=tk.LEFT, padx=5)

        # Refresh local files button
        refresh_button = ttk.Button(
            button_frame,
            text="Refresh Local Files",
            command=self._auto_select_latest_file
        )
        refresh_button.pack(side=tk.LEFT, padx=5)

    def _clear_drive_filters(self):
        """Clear all drive filters."""
        self.satellite_filter_var.set("All")
        self.subsystem_filter_var.set("All")
        # Refresh the file list
        self._list_drive_files()

    def _apply_drive_filters(self, files):
        """Apply filters to the drive files list.

        Args:
            files: List of file dictionaries with 'name' key

        Returns:
            Filtered list of files
        """
        if not files:
            return files

        filtered_files = []
        satellite_filter = self.satellite_filter_var.get()
        subsystem_filter = self.subsystem_filter_var.get()

        for file in files:
            filename = file['name'].lower()
            include_file = True

            # Apply satellite filter
            if satellite_filter != "All":
                satellite_name = satellite_filter.lower()
                if satellite_name not in filename:
                    include_file = False

            # Apply subsystem filter
            if subsystem_filter != "All" and include_file:
                subsystem_name = subsystem_filter.lower()
                # Check if the subsystem name appears in the filename
                if subsystem_name not in filename:
                    include_file = False

            if include_file:
                filtered_files.append(file)

        return filtered_files

    def _list_drive_files(self):
        """List available files from Google Drive with filtering."""
        try:
            # Clear the current list
            self.files_listbox.delete(0, tk.END)

            # Check if drive service is available
            if not self.app.drive_service:
                self.app.update_status("Google Drive service not available. Please use local files.", warning=True)
                self.files_listbox.insert(tk.END, "Google Drive not available")
                return

            # Get files from Drive through the app's drive service
            all_files = self.app.list_drive_files()

            # Apply filters
            filtered_files = self._apply_drive_filters(all_files)

            # Add filtered files to listbox
            if filtered_files and len(filtered_files) > 0:
                for file in filtered_files:
                    self.files_listbox.insert(tk.END, file['name'])

                # Show filter status
                satellite_filter = self.satellite_filter_var.get()
                subsystem_filter = self.subsystem_filter_var.get()

                if satellite_filter != "All" or subsystem_filter != "All":
                    filter_info = []
                    if satellite_filter != "All":
                        filter_info.append(f"Satellite: {satellite_filter}")
                    if subsystem_filter != "All":
                        filter_info.append(f"Subsystem: {subsystem_filter}")
                    filter_text = " | ".join(filter_info)
                    self.app.update_status(f"Found {len(filtered_files)} files (filtered by {filter_text}) out of {len(all_files)} total", success=True)
                else:
                    self.app.update_status(f"Found {len(filtered_files)} files in Google Drive", success=True)
            else:
                # Check if no files match the filters
                if all_files and len(all_files) > 0:
                    self.app.update_status("No files match the current filters", warning=True)
                    self.files_listbox.insert(tk.END, "No files match filters")
                else:
                    self.app.update_status("No files found in Google Drive", warning=True)
                    self.files_listbox.insert(tk.END, "No files found")
        except Exception as e:
            self.app.update_status(f"Failed to list Drive files: {str(e)}", error=True)
            self.app.log(f"Drive error: {str(e)}")
            self.files_listbox.insert(tk.END, "Error listing files")

    def _import_selected_file(self):
        """Import the selected file from Google Drive."""
        try:
            # Check if drive service is available
            if not self.app.drive_service:
                self.app.update_status("Google Drive service not available. Please use local files.", warning=True)
                return

            selection = self.files_listbox.curselection()
            if not selection:
                self.app.update_status("Please select a file to import", warning=True)
                return

            filename = self.files_listbox.get(selection[0])

            # Check if the selected item is an error message
            if filename in ["Google Drive not available", "No files found", "Error listing files"]:
                self.app.update_status("Cannot import this item", warning=True)
                return

            # Create data directory if it doesn't exist
            data_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "data")
            if not os.path.exists(data_dir):
                os.makedirs(data_dir)

            # Download file through the app's drive service
            local_path = os.path.join(data_dir, filename)
            self.app.download_drive_file(filename, local_path)

            # Auto-select the newly downloaded file
            self.input_var.set(local_path)
            self.app.update_status(f"Successfully imported {filename}", success=True)

        except Exception as e:
            self.app.update_status(f"Failed to import file: {str(e)}", error=True)
            self.app.log(f"Import error: {str(e)}")

    def _auto_select_latest_file(self):
        """Automatically select the most recent CSV file from the data directory."""
        try:
            # Define the data directory path
            data_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "data")

            # Create the directory if it doesn't exist
            if not os.path.exists(data_dir):
                os.makedirs(data_dir)

            # Find all CSV files in the data directory
            csv_files = glob.glob(os.path.join(data_dir, "*.csv"))

            if csv_files:
                # Get the most recent file based on modification time
                latest_file = max(csv_files, key=os.path.getmtime)
                self.input_var.set(latest_file)
                # Update status bar instead of showing a popup
                self.app.update_status(f"Selected latest file: {os.path.basename(latest_file)}", success=True)
        except Exception as e:
            # Log error but don't show popup
            self.app.log(f"Auto-select error: {str(e)}")
            self.app.update_status("Could not auto-select latest file", warning=True)

    def _create_input_section(self):
        """Create the input selection section."""
        input_frame = ttk.LabelFrame(self.frame, text="Input Selection")
        input_frame.pack(fill=tk.X, padx=10, pady=5)

        # File selection
        file_frame = ttk.Frame(input_frame)
        file_frame.pack(fill=tk.X, padx=5, pady=5)

        self.input_var = tk.StringVar()
        input_entry = ttk.Entry(file_frame, textvariable=self.input_var, width=50)
        input_entry.pack(side=tk.LEFT, padx=5)

        browse_button = ttk.Button(
            file_frame,
            text="Browse",
            command=self._browse_input
        )
        browse_button.pack(side=tk.LEFT, padx=5)

        # Add auto-select button
        auto_select_button = ttk.Button(
            file_frame,
            text="Auto-select Latest",
            command=self._auto_select_latest_file
        )
        auto_select_button.pack(side=tk.LEFT, padx=5)

    def _create_control_section(self):
        """Create the processing control section."""
        control_frame = ttk.Frame(self.frame)
        control_frame.pack(fill=tk.X, padx=10, pady=5)

        # Start button
        self.start_button = ttk.Button(
            control_frame,
            text="Start Processing",
            command=self._start_processing
        )
        self.start_button.pack(side=tk.LEFT, padx=5)

        # Stop button
        self.stop_button = ttk.Button(
            control_frame,
            text="Stop",
            command=self._stop_processing,
            state=tk.DISABLED
        )
        self.stop_button.pack(side=tk.LEFT, padx=5)

    def _create_progress_section(self):
        """Create the progress section."""
        progress_frame = ttk.LabelFrame(self.frame, text="Progress")
        progress_frame.pack(fill=tk.X, padx=10, pady=5)

        # Progress bar
        # self.progress_var = tk.DoubleVar()
        # self.progress_bar = ttk.Progressbar(
        #     progress_frame,
        #     variable=self.progress_var,
        #     maximum=100
        # )
        # self.progress_bar.pack(fill=tk.X, padx=5, pady=5)

        # Status label
        # self.status_var = tk.StringVar(value="Ready")
        # status_label = ttk.Label(
        #     progress_frame,
        #     textvariable=self.status_var
        # )
        # status_label.pack(padx=5, pady=5)

    def _browse_input(self):
        """Open file dialog to select input file."""
        filename = filedialog.askopenfilename(
            title="Select Input File",
            filetypes=[
                ("CSV files", "*.csv"),
                ("All files", "*.*")
            ]
        )
        if filename:
            self.input_var.set(filename)

    def _start_processing(self):
        """Start the processing operation."""
        input_file = self.input_var.get()
        if not input_file:
            self.app.update_status("Please select an input file", warning=True)
            return

        # Update UI state
        self.start_button.configure(state=tk.DISABLED)
        self.stop_button.configure(state=tk.NORMAL)
        self.app.update_status("Processing...")

        # Start processing in the app
        self.app.process_data(input_file, None, None)

    def _stop_processing(self):
        """Stop the processing operation."""
        if self.app.processing_thread and self.app.processing_thread.is_alive():
            self.app.stop_processing = True
            self.app.update_status("Stopping...")
            self.stop_button.configure(state=tk.DISABLED)
        else:
            self.app.update_status("No processing is currently running", info=True)

    def reset_ui_state(self):
        """Reset the UI state after processing completes or fails."""
        self.start_button.configure(state=tk.NORMAL)
        self.stop_button.configure(state=tk.DISABLED)
        # self.progress_var.set(0)
        # self.status_var.set("Ready")

    def update_progress(self, value, message=None):
        """Update the progress bar and status message."""
        # self.progress_var.set(value)
        # if message:
        #     self.status_var.set(message)

        # If processing is complete, reset UI state
        if value >= 100:
            self.reset_ui_state()
            # if not message:
            #     self.status_var.set("Complete")