"""
Results tab module for the Satellite Telemetry Processor.
"""

import tkinter as tk
from tkinter import ttk
import os
from datetime import datetime
import pandas as pd

from app.utils_pkg.common import show_error_message, show_info_message

class ResultsTab:
    """Results tab component for the application."""

    def __init__(self, notebook, app):
        """Initialize the results tab.

        Args:
            notebook: The parent notebook widget
            app: The main application instance
        """
        self.notebook = notebook
        self.app = app

        # Create main frame
        self.frame = ttk.Frame(notebook)

        # Initialize data storage
        self.results_data = None
        self.filtered_data = None

        # Create results sections
        self._create_filter_section()
        self._create_results_section()
        self._create_export_section()

    def _create_filter_section(self):
        """Create the filter section."""
        filter_frame = ttk.LabelFrame(self.frame, text="Filters")
        filter_frame.pack(fill=tk.X, padx=10, pady=5)

        # Date filter
        date_frame = ttk.Frame(filter_frame)
        date_frame.pack(fill=tk.X, padx=5, pady=5)

        date_label = ttk.Label(date_frame, text="Date:")
        date_label.pack(side=tk.LEFT, padx=5)

        self.date_var = tk.StringVar()
        date_entry = ttk.Entry(date_frame, textvariable=self.date_var, width=15)
        date_entry.pack(side=tk.LEFT, padx=5)

        # Type filter
        type_frame = ttk.Frame(filter_frame)
        type_frame.pack(fill=tk.X, padx=5, pady=5)

        type_label = ttk.Label(type_frame, text="Type:")
        type_label.pack(side=tk.LEFT, padx=5)

        type_options = [
            "All",
            "Normal",
            "Warning",
            "Error"
        ]

        self.type_var = tk.StringVar(value="All")
        type_combobox = ttk.Combobox(
            type_frame,
            textvariable=self.type_var,
            values=type_options,
            state="readonly",
            width=15
        )
        type_combobox.pack(side=tk.LEFT, padx=5)

        # Keyword filter
        keyword_frame = ttk.Frame(filter_frame)
        keyword_frame.pack(fill=tk.X, padx=5, pady=5)

        keyword_label = ttk.Label(keyword_frame, text="Keyword:")
        keyword_label.pack(side=tk.LEFT, padx=5)

        self.keyword_var = tk.StringVar()
        keyword_entry = ttk.Entry(keyword_frame, textvariable=self.keyword_var, width=30)
        keyword_entry.pack(side=tk.LEFT, padx=5)

        # Filter buttons
        button_frame = ttk.Frame(filter_frame)
        button_frame.pack(fill=tk.X, padx=5, pady=5)

        apply_button = ttk.Button(
            button_frame,
            text="Apply Filters",
            command=self._apply_filters
        )
        apply_button.pack(side=tk.LEFT, padx=5)

        clear_button = ttk.Button(
            button_frame,
            text="Clear Filters",
            command=self._clear_filters
        )
        clear_button.pack(side=tk.LEFT, padx=5)

    def _create_results_section(self):
        """Create the results section."""
        results_frame = ttk.LabelFrame(self.frame, text="Results")
        results_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # Create treeview
        self.tree = ttk.Treeview(
            results_frame,
            columns=("Date", "Time", "Type", "Message"),
            show="headings"
        )

        # Define headings
        self.tree.heading("Date", text="Date")
        self.tree.heading("Time", text="Time")
        self.tree.heading("Type", text="Type")
        self.tree.heading("Message", text="Message")

        # Define columns
        self.tree.column("Date", width=100)
        self.tree.column("Time", width=100)
        self.tree.column("Type", width=100)
        self.tree.column("Message", width=450)

        # Add scrollbars
        y_scrollbar = ttk.Scrollbar(
            results_frame,
            orient=tk.VERTICAL,
            command=self.tree.yview
        )
        self.tree.configure(yscrollcommand=y_scrollbar.set)

        x_scrollbar = ttk.Scrollbar(
            results_frame,
            orient=tk.HORIZONTAL,
            command=self.tree.xview
        )
        self.tree.configure(xscrollcommand=x_scrollbar.set)

        # Pack everything
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        y_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        x_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)

    def _create_export_section(self):
        """Create the export section."""
        export_frame = ttk.Frame(self.frame)
        export_frame.pack(fill=tk.X, padx=10, pady=5)

        export_button = ttk.Button(
            export_frame,
            text="Export Results",
            command=self._export_results
        )
        export_button.pack(side=tk.LEFT, padx=5)

        summary_button = ttk.Button(
            export_frame,
            text="Show Summary",
            command=self._show_summary
        )
        summary_button.pack(side=tk.LEFT, padx=5)

    def _apply_filters(self):
        """Apply the current filters to the results."""
        date = self.date_var.get()
        type_filter = self.type_var.get()
        keyword = self.keyword_var.get()

        # Clear current display
        for item in self.tree.get_children():
            self.tree.delete(item)

        # Apply filters and update display
        self.app.apply_filters(date, type_filter, keyword)

    def _clear_filters(self):
        """Clear all filters."""
        self.date_var.set("")
        self.type_var.set("All")
        self.keyword_var.set("")
        self._apply_filters()

    def _export_results(self):
        """Export the filtered results."""
        self.app.export_results()

    def _show_summary(self):
        """Show a summary of the results."""
        self.app.show_results_summary()

    def add_result(self, timestamp, type_, message):
        """Add a result to the treeview.

        Args:
            timestamp: The timestamp
            type_: The result type (Normal, Warning, Error)
            message: The result message
        """
        try:
            # Convert timestamp to datetime if it's a string
            if isinstance(timestamp, str):
                dt = pd.to_datetime(timestamp)
            elif isinstance(timestamp, (pd.Timestamp, datetime)):
                dt = timestamp
            else:
                # If timestamp is invalid, use current date/time
                dt = datetime.now()

            # Format date and time separately
            date_str = dt.strftime("%Y-%m-%d")
            time_str = dt.strftime("%H:%M:%S")

            # Insert into treeview with separate date and time
            self.tree.insert("", tk.END, values=(date_str, time_str, type_, message))
        except Exception as e:
            show_error_message("Add Result Error", f"Error adding result: {str(e)}")

    def clear_results(self):
        """Clear all results from the treeview."""
        for item in self.tree.get_children():
            self.tree.delete(item)







    def set_results(self, data):
        """Set the results data.

        Args:
            data: The results data (list of dictionaries or pandas DataFrame)
        """
        # Convert data to DataFrame if it's a list
        if isinstance(data, list):
            self.results_data = pd.DataFrame(data)
        else:
            self.results_data = data

        # Clear current display
        self.clear_results()

        if self.results_data is not None and not self.results_data.empty:
            # Add each row to the treeview
            for _, row in self.results_data.iterrows():
                # Format timestamp if it exists
                timestamp = row.get('timestamp', '')
                if isinstance(timestamp, (pd.Timestamp, datetime)):
                    dt = timestamp
                elif isinstance(timestamp, str):
                    try:
                        dt = pd.to_datetime(timestamp)
                    except:
                        dt = datetime.now()
                else:
                    dt = datetime.now()

                # Format date and time separately
                date_str = dt.strftime("%Y-%m-%d")
                time_str = dt.strftime("%H:%M:%S")

                # Get type and message
                type_ = row.get('type', '')
                message = row.get('message', row.get('description', ''))

                # Add to treeview with separate date and time
                self.tree.insert("", tk.END, values=(date_str, time_str, type_, message))

            self.app.update_status(f"Loaded {len(self.results_data)} results", success=True)
        else:
            self.app.update_status("No results to display", warning=True)